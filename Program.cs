using Microsoft.Extensions.Configuration;
using SignalRClientDemo.Services;

var config = new ConfigurationBuilder()
    .AddJsonFile("appsettings.json")
    .Build();

// 先登入取得 Token
var authService = new AuthService(config);
var token = await authService.LoginAndGetTokenAsync();
Console.WriteLine($"🔑 取得 Token: {token}");

// 建立 SignalR 連線
var url = config["SignalR:Url"] ?? throw new Exception("SignalR URL 未設定");
var areaCode = config["SignalR:AreaCode"] ?? "A2";

var client = new SignalRClientService(url, areaCode, token);
await client.StartAsync();

Console.WriteLine("按下 Enter 退出...");
Console.ReadLine();

await client.StopAsync();