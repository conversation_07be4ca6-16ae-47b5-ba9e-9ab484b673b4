{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"SignalRClient/1.0.0": {"dependencies": {"Microsoft.AspNetCore.SignalR.Client": "8.0.8", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "System.Net.Http.Json": "8.0.0"}, "runtime": {"SignalRClient.dll": {}}}, "Microsoft.AspNetCore.Connections.Abstractions/8.0.8": {"dependencies": {"Microsoft.Extensions.Features": "8.0.8", "System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}}}, "Microsoft.AspNetCore.Http.Connections.Client/8.0.8": {"dependencies": {"Microsoft.AspNetCore.Http.Connections.Common": "8.0.8", "Microsoft.Extensions.Logging.Abstractions": "8.0.1", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Http.Connections.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}}}, "Microsoft.AspNetCore.Http.Connections.Common/8.0.8": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "8.0.8"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Http.Connections.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}}}, "Microsoft.AspNetCore.SignalR.Client/8.0.8": {"dependencies": {"Microsoft.AspNetCore.Http.Connections.Client": "8.0.8", "Microsoft.AspNetCore.SignalR.Client.Core": "8.0.8"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}}}, "Microsoft.AspNetCore.SignalR.Client.Core/8.0.8": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "8.0.8", "Microsoft.AspNetCore.SignalR.Protocols.Json": "8.0.8", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "System.Threading.Channels": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Client.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}}}, "Microsoft.AspNetCore.SignalR.Common/8.0.8": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "8.0.8", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}}}, "Microsoft.AspNetCore.SignalR.Protocols.Json/8.0.8": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "8.0.8"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}}}, "Microsoft.Extensions.Features/8.0.8": {"runtime": {"lib/net8.0/Microsoft.Extensions.Features.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.1", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}}}, "Microsoft.Extensions.Options/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.224.6711"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.IO.Pipelines/8.0.0": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Net.Http.Json/8.0.0": {"dependencies": {"System.Text.Json": "8.0.0"}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Threading.Channels/8.0.0": {}}}, "libraries": {"SignalRClient/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Connections.Abstractions/8.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-Qf1C5WlKogCGl7jBthegWpRhxknayYUj/T5t/iFqEif9TdhpHv2o+dwgBFRNov3bdVQtK9gvmnPYsxaorBvpHA==", "path": "microsoft.aspnetcore.connections.abstractions/8.0.8", "hashPath": "microsoft.aspnetcore.connections.abstractions.8.0.8.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Client/8.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-eAOn2qAuVUo4QH+kUhj03MHshpdffT50JmoRAbtcbaKiciIcNeSW+dpa6vnrAZ2abKBCWdHPECPxzC1W8KnCtQ==", "path": "microsoft.aspnetcore.http.connections.client/8.0.8", "hashPath": "microsoft.aspnetcore.http.connections.client.8.0.8.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Common/8.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-+dH9RZFAsiqi8ATIiZGfPYpet6BFvoHCBh01uYocMyI2QUlu/vT23BYc2ciAlLq0ljFha8CQ59th4wknWdDa+g==", "path": "microsoft.aspnetcore.http.connections.common/8.0.8", "hashPath": "microsoft.aspnetcore.http.connections.common.8.0.8.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Client/8.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-a41cXpQDnGNsrAKOLtNtPVnnQxcl7ThHzlUSpgkG7fZZYAT6gBppn/lbQ8M73BcRh3p71TExpNyZ4eefLkvTUg==", "path": "microsoft.aspnetcore.signalr.client/8.0.8", "hashPath": "microsoft.aspnetcore.signalr.client.8.0.8.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Client.Core/8.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-vvAKtgewQ6E4qhIH88QRv/x9OKKI2duhzojJH6sI8eTn1yvR6cdMD780KIs6//0HXkRDJZspqiLH2jHltKQpPQ==", "path": "microsoft.aspnetcore.signalr.client.core/8.0.8", "hashPath": "microsoft.aspnetcore.signalr.client.core.8.0.8.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Common/8.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-n4WyR53huf+azYAr0upo3SVHEMQPbuGfeAGOpx+WEMxGqKtkHiWHoRkTrxL89Q4Hd1Qok4mF80o3x7yuS8qXFA==", "path": "microsoft.aspnetcore.signalr.common/8.0.8", "hashPath": "microsoft.aspnetcore.signalr.common.8.0.8.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Protocols.Json/8.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-8iqF3voCEzs4hfPNU/MVodtxfg1qpl6O0OUy5uJyzuSWfS4SKY8Vwgr78FgBGvAuRI130eDFV5BHWXAOoMG4eQ==", "path": "microsoft.aspnetcore.signalr.protocols.json/8.0.8", "hashPath": "microsoft.aspnetcore.signalr.protocols.json.8.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "path": "microsoft.extensions.configuration.json/8.0.0", "hashPath": "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fGLiCRLMYd00JYpClraLjJTNKLmMJPnqxMaiRzEBIIvevlzxz33mXy39Lkd48hu1G+N21S7QpaO5ZzKsI6FRuA==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.1", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Features/8.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-twQ7HVVebAchBSLM5VsPeS/rwri3HJwxv22+wUQppr0ByUke97Ipw38gwGqFAnG2dz08N5LicKW2wpNtRng6Fw==", "path": "microsoft.extensions.features/8.0.8", "hashPath": "microsoft.extensions.features.8.0.8.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-RIFgaqoaINxkM2KTOw72dmilDmTrYA0ns2KW4lDz4gZ2+o6IQ894CzmdL3StM2oh7QQq44nCWiqKqc4qUI9Jmg==", "path": "microsoft.extensions.logging.abstractions/8.0.1", "hashPath": "microsoft.extensions.logging.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "path": "microsoft.extensions.options/8.0.2", "hashPath": "microsoft.extensions.options.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "System.IO.Pipelines/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "path": "system.io.pipelines/8.0.0", "hashPath": "system.io.pipelines.8.0.0.nupkg.sha512"}, "System.Net.Http.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-48Bxrd6zcGeQzS4GMEDVjuqCcAw/9wcEWnIu48FQJ5IzfKPiMR1nGtz9LrvGzU4+3TLbx/9FDlGmCUeLin1Eqg==", "path": "system.net.http.json/8.0.0", "hashPath": "system.net.http.json.8.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>drZ<PERSON>2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "path": "system.text.json/8.0.0", "hashPath": "system.text.json.8.0.0.nupkg.sha512"}, "System.Threading.Channels/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CMaFr7v+57RW7uZfZkPExsPB6ljwzhjACWW1gfU35Y56rk72B/Wu+sTqxVmGSk4SFUlPc3cjeKND0zktziyjBA==", "path": "system.threading.channels/8.0.0", "hashPath": "system.threading.channels.8.0.0.nupkg.sha512"}}}