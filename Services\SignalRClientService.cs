using Microsoft.AspNetCore.SignalR.Client;

namespace SignalRClientDemo.Services
{
    public class SignalRClientService
    {
        private readonly HubConnection _connection;

        public SignalRClientService(string url, string areaCode, string token)
        {
            var fullUrl = $"{url}?AreaCode={areaCode}&id={token}";
            _connection = new HubConnectionBuilder()
                    .WithUrl(fullUrl, options =>
                    {
                        // 配置 WebSocket 的 TLS 憑證驗證（僅用於測試）
                        options.HttpMessageHandlerFactory = (message) =>
                        {
                            if (message is HttpClientHandler clientHandler)
                            {
                                clientHandler.ServerCertificateCustomValidationCallback =
                                    (msg, cert, chain, errors) => true; // 忽略自簽憑證（僅限測試）
                            }
                            return message;
                        };
                    })
                    .WithAutomaticReconnect()
                    .Build();
        }

        public async Task StartAsync()
        {
            _connection.On<string>("ReceiveMessage", (message) =>
            {
                Console.WriteLine($"📩 收到訊息: {message}");
            });

            _connection.Reconnected += connectionId =>
            {
                Console.WriteLine("🔄 重新連線成功");
                return Task.CompletedTask;
            };

            _connection.Closed += async (error) =>
            {
                Console.WriteLine($"⚠️ 連線中斷: {error?.Message}");
                await Task.Delay(3000);
                await StartAsync();
            };

            await _connection.StartAsync();
            Console.WriteLine("✅ 已連線到 SignalR");
        }

        public async Task StopAsync()
        {
            await _connection.StopAsync();
            Console.WriteLine("🛑 已中斷連線");
        }
    }
}
