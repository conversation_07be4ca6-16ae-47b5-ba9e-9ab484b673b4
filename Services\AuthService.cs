using System.Net.Http.Json;
using System.Net.Http;
using Microsoft.Extensions.Configuration;

namespace SignalRClientDemo.Services
{
    public class AuthService
    {
        private readonly string _username;
        private readonly string _password;
        private readonly string _loginUrl;
        private readonly HttpClient _httpClient;

        public AuthService(IConfiguration config)
        {
            _username = config["AuthConfig:Username"] ?? throw new Exception("Username 未設定");
            _password = config["AuthConfig:Password"] ?? throw new Exception("Password 未設定");
            _loginUrl = config["AuthConfig:LoginUrl"] ?? throw new Exception("LoginUrl 未設定");

            _httpClient = new HttpClient();
        }

        public async Task<string> LoginAndGetTokenAsync()
        {
            var loginData = new
            {
                UserAccount = _username,
                UserPassword = _password
            };

            var response = await _httpClient.PostAsJsonAsync(_loginUrl, loginData);

            if (!response.IsSuccessStatusCode)
                throw new Exception($"登入失敗: {response.StatusCode}");

            var json = await response.Content.ReadFromJsonAsync<Dictionary<string, object>>();

            if (json == null || !json.ContainsKey("token"))
                throw new Exception("回應中沒有 token 欄位");

            return json["token"]!.ToString()!;
        }
    }
}
